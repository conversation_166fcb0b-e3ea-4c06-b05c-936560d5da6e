'use client'

import { DashboardLayout } from '@/components/dashboard/layout'
import { BlogForm } from '@/components/forms/blog-form'

interface EditBlogPostPageProps {
  params: {
    id: string
  }
}

export default function EditBlogPostPage({ params }: EditBlogPostPageProps) {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Blog Post</h1>
          <p className="text-muted-foreground">
            Update your blog post content
          </p>
        </div>

        <BlogForm postId={params.id} />
      </div>
    </DashboardLayout>
  )
}

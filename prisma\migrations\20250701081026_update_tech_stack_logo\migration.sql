/*
  Warnings:

  - You are about to drop the column `icon` on the `tech_stack` table. All the data in the column will be lost.
  - Added the required column `logo` to the `tech_stack` table without a default value. This is not possible if the table is not empty.

*/

-- Add the logo column with a default value first
ALTER TABLE "tech_stack" ADD COLUMN "logo" TEXT NOT NULL DEFAULT '';

-- Update existing records with logo URLs based on their names
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg' WHERE "name" = 'React';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg' WHERE "name" = 'Next.js';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg' WHERE "name" = 'TypeScript';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tailwindcss/tailwindcss-plain.svg' WHERE "name" = 'Tailwind CSS';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg' WHERE "name" = 'Node.js';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg' WHERE "name" = 'PostgreSQL';
UPDATE "tech_stack" SET "logo" = 'https://www.prisma.io/images/favicon-32x32.png' WHERE "name" = 'Prisma';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg' WHERE "name" = 'MongoDB';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg' WHERE "name" = 'Git';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg' WHERE "name" = 'Docker';
UPDATE "tech_stack" SET "logo" = 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg' WHERE "name" = 'AWS';
UPDATE "tech_stack" SET "logo" = 'https://assets.vercel.com/image/upload/v1662130559/nextjs/Icon_light_background.png' WHERE "name" = 'Vercel';

-- Drop the icon column
ALTER TABLE "tech_stack" DROP COLUMN "icon";

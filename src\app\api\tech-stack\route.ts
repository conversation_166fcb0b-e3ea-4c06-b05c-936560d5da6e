import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const techStack = await prisma.techStack.findMany({
      orderBy: [
        { category: 'asc' },
        { order: 'asc' },
      ],
    })

    return withCors(NextResponse.json(techStack))
  } catch (error) {
    console.error('Error fetching tech stack:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch tech stack' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      logo,
      color,
      category,
      order,
      published,
    } = body

    const tech = await prisma.techStack.create({
      data: {
        name,
        logo,
        color,
        category,
        order: order || 0,
        published: published !== undefined ? published : true,
      },
    })

    return withCors(NextResponse.json(tech, { status: 201 }))
  } catch (error) {
    console.error('Error creating tech:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create tech' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}

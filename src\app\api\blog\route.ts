import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const blogPosts = await prisma.blogPost.findMany({
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return withCors(NextResponse.json(blogPosts))
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      slug,
      excerpt,
      content,
      image,
      category,
      tags,
      published,
      featured,
      readTime,
    } = body

    const blogPost = await prisma.blogPost.create({
      data: {
        title,
        slug,
        excerpt,
        content,
        image,
        category,
        tags,
        published: published || false,
        featured: featured || false,
        readTime,
        publishedAt: published ? new Date() : null,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return withCors(NextResponse.json(blogPost, { status: 201 }))
  } catch (error) {
    console.error('Error creating blog post:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}

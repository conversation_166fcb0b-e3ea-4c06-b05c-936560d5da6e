import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const tech = await prisma.techStack.findUnique({
      where: { id },
    })

    if (!tech) {
      return NextResponse.json(
        { error: 'Tech not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(tech)
  } catch (error) {
    console.error('Error fetching tech:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tech' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const {
      name,
      logo,
      color,
      category,
      order,
      published,
    } = body

    const tech = await prisma.techStack.update({
      where: { id },
      data: {
        name,
        logo,
        color,
        category,
        order,
        published,
      },
    })

    return NextResponse.json(tech)
  } catch (error) {
    console.error('Error updating tech:', error)
    return NextResponse.json(
      { error: 'Failed to update tech' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    await prisma.techStack.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Tech deleted successfully' })
  } catch (error) {
    console.error('Error deleting tech:', error)
    return NextResponse.json(
      { error: 'Failed to delete tech' },
      { status: 500 }
    )
  }
}
